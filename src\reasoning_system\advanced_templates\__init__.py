#!/usr/bin/env python3
"""
Advanced Templates for Enhanced Reasoning System
===============================================

Advanced template system for generating diverse, natural trading reasoning.
Provides natural language generation, template variations, and contextual phrases.
"""

from .natural_language_generator import NaturalLanguageGenerator
from .template_variations import TemplateVariations

__all__ = [
    'NaturalLanguageGenerator',
    'TemplateVariations'
]
